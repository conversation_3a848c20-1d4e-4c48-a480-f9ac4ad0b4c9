
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ArrowUp, User, Search, Check } from 'lucide-react';
import { Link } from 'react-router-dom';

const StudentDashboard = () => {
  const studentSkills = [
    { name: 'React.js', level: 85, category: 'Frontend' },
    { name: 'JavaScript', level: 90, category: 'Programming' },
    { name: 'Python', level: 75, category: 'Programming' },
    { name: 'UI/UX Design', level: 70, category: 'Design' },
    { name: 'Digital Marketing', level: 60, category: 'Marketing' }
  ];

  const recommendedJobs = [
    {
      id: 1,
      title: 'Frontend Developer Intern',
      company: 'TechCorp Inc.',
      location: 'Remote',
      match: 95,
      skills: ['React', 'JavaScript', 'CSS'],
      type: 'Internship'
    },
    {
      id: 2,
      title: 'UI/UX Design Intern',
      company: 'Design Studio',
      location: 'New York',
      match: 88,
      skills: ['Figma', 'User Research', 'Prototyping'],
      type: 'Internship'
    },
    {
      id: 3,
      title: 'Marketing Assistant',
      company: 'StartupCo',
      location: 'San Francisco',
      match: 82,
      skills: ['Digital Marketing', 'Analytics', 'Content'],
      type: 'Part-time'
    }
  ];

  const applications = [
    {
      id: 1,
      position: 'Frontend Developer Intern',
      company: 'TechCorp Inc.',
      status: 'Interview Scheduled',
      appliedDate: '2024-01-20',
      stage: 'interview'
    },
    {
      id: 2,
      position: 'Marketing Intern',
      company: 'GrowthLab',
      status: 'Assessment Complete',
      appliedDate: '2024-01-18',
      stage: 'assessment'
    },
    {
      id: 3,
      position: 'Data Analyst Intern',
      company: 'DataCorp',
      status: 'Application Submitted',
      appliedDate: '2024-01-22',
      stage: 'applied'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">SM</span>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                SkillMatch.ai
              </span>
            </Link>
            <div className="flex items-center space-x-4">
              <Badge variant="outline">Student</Badge>
              <Button variant="ghost" size="sm">
                <User className="h-4 w-4 mr-2" />
                Profile
              </Button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Student Dashboard</h1>
          <p className="text-gray-600 mt-2">Track your skills, find opportunities, and grow your career</p>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="jobs">Job Recommendations</TabsTrigger>
            <TabsTrigger value="applications">My Applications</TabsTrigger>
            <TabsTrigger value="skills">Skill Development</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Applications Sent</CardTitle>
                  <ArrowUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">8</div>
                  <p className="text-xs text-muted-foreground">+3 this week</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Interview Invites</CardTitle>
                  <User className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">3</div>
                  <p className="text-xs text-muted-foreground">2 pending responses</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Skill Score</CardTitle>
                  <Check className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">87%</div>
                  <p className="text-xs text-muted-foreground">Top 15% in your field</p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Your Skill Profile</CardTitle>
                  <CardDescription>Track your skill development progress</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {studentSkills.slice(0, 4).map((skill, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">{skill.name}</span>
                          <span className="text-sm text-gray-600">{skill.level}%</span>
                        </div>
                        <Progress value={skill.level} className="h-2" />
                      </div>
                    ))}
                  </div>
                  <Button className="w-full mt-4" variant="outline">
                    View Complete Profile
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recommended for You</CardTitle>
                  <CardDescription>AI-curated opportunities based on your skills</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recommendedJobs.slice(0, 2).map((job) => (
                      <div key={job.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h3 className="font-semibold">{job.title}</h3>
                            <p className="text-sm text-gray-600">{job.company} • {job.location}</p>
                          </div>
                          <Badge className="bg-green-100 text-green-800">
                            {job.match}% Match
                          </Badge>
                        </div>
                        <div className="flex gap-2 mb-3">
                          {job.skills.map((skill, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {skill}
                            </Badge>
                          ))}
                        </div>
                        <Button size="sm" className="bg-gradient-to-r from-purple-600 to-blue-600">
                          Apply Now
                        </Button>
                      </div>
                    ))}
                  </div>
                  <Button className="w-full mt-4" variant="outline">
                    View All Recommendations
                  </Button>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Your latest actions and updates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm">Completed skill assessment for React.js</p>
                      <p className="text-xs text-gray-500">2 hours ago</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm">Applied to Frontend Developer position at TechCorp</p>
                      <p className="text-xs text-gray-500">1 day ago</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm">Received interview invitation from Design Studio</p>
                      <p className="text-xs text-gray-500">2 days ago</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="jobs" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Job Recommendations</h2>
              <div className="flex items-center space-x-2">
                <Search className="h-4 w-4 text-gray-400" />
                <input 
                  type="text" 
                  placeholder="Search opportunities..." 
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
            </div>

            <div className="space-y-4">
              {recommendedJobs.map((job) => (
                <Card key={job.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-xl font-semibold">{job.title}</h3>
                          <Badge className="bg-green-100 text-green-800">
                            {job.match}% Match
                          </Badge>
                          <Badge variant="outline">{job.type}</Badge>
                        </div>
                        <p className="text-gray-600 mb-3">{job.company} • {job.location}</p>
                        <div className="flex gap-2 mb-4">
                          {job.skills.map((skill, index) => (
                            <Badge key={index} variant="secondary">
                              {skill}
                            </Badge>
                          ))}
                        </div>
                        <div className="flex space-x-2">
                          <Button className="bg-gradient-to-r from-purple-600 to-blue-600">
                            Apply Now
                          </Button>
                          <Button variant="outline">
                            Save for Later
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="applications" className="space-y-6">
            <h2 className="text-2xl font-bold">My Applications</h2>
            
            <div className="space-y-4">
              {applications.map((app) => (
                <Card key={app.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-lg font-semibold">{app.position}</h3>
                        <p className="text-gray-600">{app.company}</p>
                        <p className="text-sm text-gray-500">Applied on {app.appliedDate}</p>
                      </div>
                      <div className="text-right">
                        <Badge 
                          className={
                            app.stage === 'interview' ? 'bg-green-100 text-green-800' :
                            app.stage === 'assessment' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }
                        >
                          {app.status}
                        </Badge>
                        <div className="mt-2 space-x-2">
                          <Button size="sm" variant="outline">View Details</Button>
                          {app.stage === 'interview' && (
                            <Button size="sm" className="bg-gradient-to-r from-purple-600 to-blue-600">
                              Prepare Interview
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="skills" className="space-y-6">
            <h2 className="text-2xl font-bold">Skill Development</h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Current Skills</CardTitle>
                  <CardDescription>Your verified skill levels</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {studentSkills.map((skill, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <div>
                            <span className="text-sm font-medium">{skill.name}</span>
                            <Badge variant="outline" className="ml-2 text-xs">
                              {skill.category}
                            </Badge>
                          </div>
                          <span className="text-sm text-gray-600">{skill.level}%</span>
                        </div>
                        <Progress value={skill.level} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Skill Recommendations</CardTitle>
                  <CardDescription>Based on your career goals and market demand</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 border border-dashed border-gray-300 rounded-lg">
                      <h4 className="font-medium text-sm mb-2">Recommended: TypeScript</h4>
                      <p className="text-xs text-gray-600 mb-3">High demand in frontend development roles</p>
                      <Button size="sm" variant="outline">Start Learning</Button>
                    </div>
                    <div className="p-4 border border-dashed border-gray-300 rounded-lg">
                      <h4 className="font-medium text-sm mb-2">Recommended: Node.js</h4>
                      <p className="text-xs text-gray-600 mb-3">Complete your full-stack development skills</p>
                      <Button size="sm" variant="outline">Start Learning</Button>
                    </div>
                    <div className="p-4 border border-dashed border-gray-300 rounded-lg">
                      <h4 className="font-medium text-sm mb-2">Recommended: Data Analysis</h4>
                      <p className="text-xs text-gray-600 mb-3">Growing field with high salary potential</p>
                      <Button size="sm" variant="outline">Start Learning</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Take a Skill Assessment</CardTitle>
                <CardDescription>Get verified badges and improve your profile</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 border rounded-lg text-center">
                    <h4 className="font-medium mb-2">JavaScript Assessment</h4>
                    <p className="text-sm text-gray-600 mb-3">30 questions • 45 minutes</p>
                    <Button size="sm" className="w-full">Start Assessment</Button>
                  </div>
                  <div className="p-4 border rounded-lg text-center">
                    <h4 className="font-medium mb-2">React.js Assessment</h4>
                    <p className="text-sm text-gray-600 mb-3">25 questions • 40 minutes</p>
                    <Button size="sm" className="w-full">Start Assessment</Button>
                  </div>
                  <div className="p-4 border rounded-lg text-center">
                    <h4 className="font-medium mb-2">Python Assessment</h4>
                    <p className="text-sm text-gray-600 mb-3">35 questions • 50 minutes</p>
                    <Button size="sm" className="w-full">Start Assessment</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default StudentDashboard;
