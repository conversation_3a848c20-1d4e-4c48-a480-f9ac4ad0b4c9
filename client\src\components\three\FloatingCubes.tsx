import React, { useRef, useMemo } from "react";
import { Canvas, useFrame } from "@react-three/fiber";
// import { Float, OrbitControls } from "@react-three/drei";
import * as THREE from "three";

interface FloatingCubeProps {
  position: [number, number, number];
  color: string;
  scale: number;
}

const FloatingCube: React.FC<FloatingCubeProps> = ({ position, color, scale }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.elapsedTime * 0.5;
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.3;
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + position[0]) * 0.5;
    }
  });

  return (
    <mesh ref={meshRef} position={position} scale={scale}>
      <boxGeometry args={[1, 1, 1]} />
      <meshStandardMaterial color={color} />
    </mesh>
  );
};

const Particle: React.FC<{ position: [number, number, number] }> = ({ position }) => {
  const meshRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime * 2 + position[0]) * 0.3;
      meshRef.current.rotation.z = state.clock.elapsedTime * 0.5;
    }
  });

  return (
    <mesh ref={meshRef} position={position}>
      <sphereGeometry args={[0.05, 8, 8]} />
      <meshStandardMaterial color="#8b5cf6" emissive="#8b5cf6" emissiveIntensity={0.2} />
    </mesh>
  );
};

const FloatingCubesScene: React.FC = () => {
  const cubes = useMemo(
    () => [
      { position: [-3, 0, 0] as [number, number, number], color: "#8b5cf6", scale: 0.8 },
      { position: [0, 1, -2] as [number, number, number], color: "#3b82f6", scale: 1.2 },
      { position: [3, -1, 1] as [number, number, number], color: "#6366f1", scale: 1.0 },
      { position: [-2, 2, 2] as [number, number, number], color: "#8b5cf6", scale: 0.6 },
      { position: [2, -2, -1] as [number, number, number], color: "#3b82f6", scale: 0.9 },
    ],
    []
  );

  const particles = useMemo(() => {
    const particleArray = [];
    for (let i = 0; i < 50; i++) {
      particleArray.push({
        position: [(Math.random() - 0.5) * 20, (Math.random() - 0.5) * 20, (Math.random() - 0.5) * 20] as [
          number,
          number,
          number
        ],
      });
    }
    return particleArray;
  }, []);

  return (
    <>
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} intensity={1} />
      <pointLight position={[-10, -10, -10]} intensity={0.5} color="#8b5cf6" />

      {cubes.map((cube, index) => (
        <FloatingCube key={index} position={cube.position} color={cube.color} scale={cube.scale} />
      ))}

      {particles.map((particle, index) => (
        <Particle key={index} position={particle.position} />
      ))}

      <mesh position={[0, 3, 0]}>
        <torusGeometry args={[1, 0.3, 16, 100]} />
        <meshStandardMaterial color="#8b5cf6" emissive="#8b5cf6" emissiveIntensity={0.1} />
      </mesh>
    </>
  );
};

interface FloatingCubesProps {
  className?: string;
}

const FloatingCubes: React.FC<FloatingCubesProps> = ({ className = "" }) => {
  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas camera={{ position: [0, 0, 8], fov: 60 }}>
        <FloatingCubesScene />
      </Canvas>
    </div>
  );
};

export default FloatingCubes;
