import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Check, X, Star, Zap, Crown, Rocket, Users, Building, Sparkles } from "lucide-react";
import { Link } from "react-router-dom";
import AnimatedSection from "@/components/animations/AnimatedSection";
import { AnimatedCard, StaggeredContainer, AnimatedButton } from "@/components/animations/AnimatedSection";

const Pricing = () => {
  const [isAnnual, setIsAnnual] = useState(false);

  const plans = [
    {
      name: "Starter",
      icon: Rocket,
      description: "Perfect for small teams getting started",
      monthlyPrice: 0,
      annualPrice: 0,
      color: "from-gray-600 to-gray-700",
      borderColor: "border-gray-200",
      popular: false,
      features: [
        { name: "Up to 5 job postings", included: true },
        { name: "Basic AI matching", included: true },
        { name: "Standard support", included: true },
        { name: "Basic analytics", included: true },
        { name: "Email notifications", included: true },
        { name: "Advanced AI features", included: false },
        { name: "Priority support", included: false },
        { name: "Custom integrations", included: false },
        { name: "Dedicated account manager", included: false },
      ],
    },
    {
      name: "Professional",
      icon: Star,
      description: "For growing companies and teams",
      monthlyPrice: 49,
      annualPrice: 39,
      color: "from-purple-600 to-blue-600",
      borderColor: "border-purple-500",
      popular: true,
      features: [
        { name: "Unlimited job postings", included: true },
        { name: "Advanced AI matching", included: true },
        { name: "Priority support", included: true },
        { name: "Advanced analytics", included: true },
        { name: "Real-time notifications", included: true },
        { name: "API access", included: true },
        { name: "Team collaboration", included: true },
        { name: "Custom integrations", included: false },
        { name: "Dedicated account manager", included: false },
      ],
    },
    {
      name: "Enterprise",
      icon: Crown,
      description: "For large organizations with custom needs",
      monthlyPrice: 199,
      annualPrice: 159,
      color: "from-indigo-600 to-purple-600",
      borderColor: "border-indigo-500",
      popular: false,
      features: [
        { name: "Everything in Professional", included: true },
        { name: "Custom AI training", included: true },
        { name: "Dedicated account manager", included: true },
        { name: "Custom integrations", included: true },
        { name: "White-label solution", included: true },
        { name: "SLA guarantee", included: true },
        { name: "Advanced security", included: true },
        { name: "Custom reporting", included: true },
        { name: "24/7 phone support", included: true },
      ],
    },
  ];

  const addOns = [
    {
      name: "Advanced Analytics",
      description: "Deep insights and custom reports",
      price: 29,
      icon: Sparkles,
    },
    {
      name: "API Access",
      description: "Full API access for integrations",
      price: 19,
      icon: Zap,
    },
    {
      name: "Priority Support",
      description: "24/7 priority customer support",
      price: 39,
      icon: Users,
    },
    {
      name: "Custom Training",
      description: "Personalized AI model training",
      price: 99,
      icon: Building,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/80 backdrop-blur-md border-b border-gray-200 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">SM</span>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                SkillMatch.ai
              </span>
            </Link>
            <div className="flex items-center space-x-4">
              <Link
                to="/about"
                className="px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium"
              >
                About
              </Link>
              <Link
                to="/features"
                className="px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium"
              >
                Features
              </Link>
              <Link
                to="/contact"
                className="px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium"
              >
                Contact
              </Link>
              <Link to="/login">
                <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <AnimatedSection animation="fadeIn">
            <Badge className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 text-sm font-medium mb-6">
              💰 Simple, Transparent Pricing
            </Badge>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Choose Your
              <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent block">
                Perfect Plan
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Start free and scale as you grow. No hidden fees, no surprises. Cancel anytime with our flexible pricing
              plans.
            </p>

            {/* Billing Toggle */}
            <div className="flex items-center justify-center space-x-4 mb-12">
              <span className={`text-lg ${!isAnnual ? "text-gray-900 font-semibold" : "text-gray-500"}`}>Monthly</span>
              <Switch checked={isAnnual} onCheckedChange={setIsAnnual} className="data-[state=checked]:bg-purple-600" />
              <span className={`text-lg ${isAnnual ? "text-gray-900 font-semibold" : "text-gray-500"}`}>Annual</span>
              {isAnnual && <Badge className="bg-green-100 text-green-700 ml-2">Save 20%</Badge>}
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <StaggeredContainer className="grid md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <AnimatedCard key={index} hoverScale={1.02}>
                <Card
                  className={`border-2 ${plan.borderColor} ${
                    plan.popular ? "ring-2 ring-purple-500 ring-opacity-50" : ""
                  } relative overflow-hidden`}
                >
                  {plan.popular && (
                    <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-center py-2 text-sm font-medium">
                      Most Popular
                    </div>
                  )}

                  <CardHeader className={plan.popular ? "pt-12" : ""}>
                    <div className="flex items-center justify-between mb-4">
                      <div
                        className={`w-12 h-12 bg-gradient-to-r ${plan.color} rounded-lg flex items-center justify-center`}
                      >
                        <plan.icon className="h-6 w-6 text-white" />
                      </div>
                      {plan.popular && <Badge className="bg-purple-100 text-purple-700">Recommended</Badge>}
                    </div>

                    <CardTitle className="text-2xl">{plan.name}</CardTitle>
                    <CardDescription className="text-gray-600 mb-4">{plan.description}</CardDescription>

                    <div className="mb-6">
                      <div className="flex items-baseline">
                        <span className="text-4xl font-bold text-gray-900">
                          ${isAnnual ? plan.annualPrice : plan.monthlyPrice}
                        </span>
                        <span className="text-gray-500 ml-2">{plan.monthlyPrice === 0 ? "" : "/month"}</span>
                      </div>
                      {isAnnual && plan.monthlyPrice > 0 && (
                        <p className="text-sm text-gray-500 mt-1">
                          Billed annually (${(isAnnual ? plan.annualPrice : plan.monthlyPrice) * 12}/year)
                        </p>
                      )}
                    </div>
                  </CardHeader>

                  <CardContent>
                    <ul className="space-y-3 mb-8">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center">
                          {feature.included ? (
                            <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                          ) : (
                            <X className="h-5 w-5 text-gray-300 mr-3 flex-shrink-0" />
                          )}
                          <span className={feature.included ? "text-gray-900" : "text-gray-400"}>{feature.name}</span>
                        </li>
                      ))}
                    </ul>

                    <AnimatedButton
                      variant="bounce"
                      className={`w-full py-3 rounded-lg font-medium transition-all duration-300 ${
                        plan.popular
                          ? "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
                          : "border border-gray-300 hover:border-purple-300 hover:bg-purple-50 text-gray-700"
                      }`}
                    >
                      {plan.monthlyPrice === 0 ? "Start Free" : "Get Started"}
                    </AnimatedButton>
                  </CardContent>
                </Card>
              </AnimatedCard>
            ))}
          </StaggeredContainer>
        </div>
      </section>

      {/* Add-ons */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <AnimatedSection animation="fadeIn" className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Add-ons & Extensions</h2>
            <p className="text-xl text-gray-600">Enhance your plan with additional features</p>
          </AnimatedSection>

          <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {addOns.map((addon, index) => (
              <AnimatedCard key={index} hoverScale={1.05}>
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 text-center">
                  <CardHeader>
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <addon.icon className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-lg">{addon.name}</CardTitle>
                    <CardDescription className="text-gray-600">{addon.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-gray-900 mb-4">${addon.price}/month</div>
                    <Button variant="outline" className="w-full">
                      Add to Plan
                    </Button>
                  </CardContent>
                </Card>
              </AnimatedCard>
            ))}
          </StaggeredContainer>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <AnimatedSection animation="fadeIn" className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Pricing FAQ</h2>
            <p className="text-xl text-gray-600">Common questions about our pricing</p>
          </AnimatedSection>

          <StaggeredContainer className="space-y-6">
            {[
              {
                question: "Can I change plans anytime?",
                answer:
                  "Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately and we'll prorate any billing differences.",
              },
              {
                question: "Is there a free trial?",
                answer:
                  "Yes, we offer a 14-day free trial of our Professional plan. No credit card required to start your trial.",
              },
              {
                question: "What payment methods do you accept?",
                answer:
                  "We accept all major credit cards, PayPal, and bank transfers for annual plans. Enterprise customers can also pay by invoice.",
              },
              {
                question: "Do you offer discounts for nonprofits?",
                answer:
                  "Yes! We offer special pricing for nonprofits, educational institutions, and startups. Contact our sales team for details.",
              },
            ].map((faq, index) => (
              <AnimatedCard key={index}>
                <Card className="border-0 shadow-sm">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
                    <p className="text-gray-600">{faq.answer}</p>
                  </CardContent>
                </Card>
              </AnimatedCard>
            ))}
          </StaggeredContainer>
        </div>
      </section>

      {/* CTA */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-blue-600">
        <div className="max-w-4xl mx-auto text-center">
          <AnimatedSection animation="scale">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
            <p className="text-xl text-purple-100 mb-8">Join thousands of companies already using SkillMatch.ai</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/signup">
                <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
                  Start Free Trial
                </Button>
              </Link>
              <Link to="/contact">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-purple-600"
                >
                  Contact Sales
                </Button>
              </Link>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </div>
  );
};

export default Pricing;
