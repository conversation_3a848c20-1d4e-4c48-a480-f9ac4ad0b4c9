import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Mail, Phone, MapPin, Clock, MessageCircle, Send, CheckCircle, Globe, Users, Headphones } from "lucide-react";
import { Link } from "react-router-dom";
import AnimatedSection from "@/components/animations/AnimatedSection";
import { AnimatedC<PERSON>, StaggeredContainer, AnimatedButton } from "@/components/animations/AnimatedSection";

const Contact = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    subject: "",
    message: "",
  });

  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Simulate form submission
    setIsSubmitted(true);
    setTimeout(() => setIsSubmitted(false), 3000);
  };

  const contactMethods = [
    {
      icon: Mail,
      title: "Email Us",
      description: "Send us an email and we'll respond within 24 hours",
      contact: "<EMAIL>",
      action: "mailto:<EMAIL>",
      color: "from-purple-600 to-blue-600",
    },
    {
      icon: Phone,
      title: "Call Us",
      description: "Speak directly with our team during business hours",
      contact: "+****************",
      action: "tel:+***********",
      color: "from-blue-600 to-indigo-600",
    },
    {
      icon: MessageCircle,
      title: "Live Chat",
      description: "Get instant support through our live chat system",
      contact: "Available 24/7",
      action: "#",
      color: "from-indigo-600 to-purple-600",
    },
    {
      icon: MapPin,
      title: "Visit Us",
      description: "Come visit our headquarters in San Francisco",
      contact: "123 Innovation St, SF, CA",
      action: "#",
      color: "from-purple-600 to-pink-600",
    },
  ];

  const offices = [
    {
      city: "San Francisco",
      address: "123 Innovation Street, Suite 100",
      phone: "+****************",
      email: "<EMAIL>",
    },
    {
      city: "New York",
      address: "456 Tech Avenue, Floor 25",
      phone: "+****************",
      email: "<EMAIL>",
    },
    {
      city: "London",
      address: "789 Digital Lane, Level 15",
      phone: "+44 20 1234 5678",
      email: "<EMAIL>",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/80 backdrop-blur-md border-b border-gray-200 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">SM</span>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                SkillMatch.ai
              </span>
            </Link>
            <div className="flex items-center space-x-4">
              <Link
                to="/about"
                className="px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium"
              >
                About
              </Link>
              <Link
                to="/features"
                className="px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium"
              >
                Features
              </Link>
              <Link
                to="/pricing"
                className="px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium"
              >
                Pricing
              </Link>
              <Link to="/login">
                <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <AnimatedSection animation="fadeIn">
            <Badge className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 text-sm font-medium mb-6">
              💬 Get in Touch
            </Badge>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Let's Start a
              <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent block">
                Conversation
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Have questions about SkillMatch.ai? Want to see a demo? Or ready to get started? We're here to help you
              transform your hiring process.
            </p>
          </AnimatedSection>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <AnimatedSection animation="fadeIn" className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">How Can We Help?</h2>
            <p className="text-xl text-gray-600">Choose the best way to reach us</p>
          </AnimatedSection>

          <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {contactMethods.map((method, index) => (
              <AnimatedCard key={index} hoverScale={1.05}>
                <a href={method.action} className="block">
                  <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 h-full text-center">
                    <CardHeader>
                      <div
                        className={`w-12 h-12 bg-gradient-to-r ${method.color} rounded-lg flex items-center justify-center mx-auto mb-4`}
                      >
                        <method.icon className="h-6 w-6 text-white" />
                      </div>
                      <CardTitle className="text-xl">{method.title}</CardTitle>
                      <CardDescription className="text-gray-600">{method.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="font-semibold text-purple-600">{method.contact}</p>
                    </CardContent>
                  </Card>
                </a>
              </AnimatedCard>
            ))}
          </StaggeredContainer>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <AnimatedSection animation="slideLeft">
              <Card className="border-0 shadow-xl">
                <CardHeader>
                  <CardTitle className="text-2xl">Send us a Message</CardTitle>
                  <CardDescription>
                    Fill out the form below and we'll get back to you as soon as possible.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isSubmitted ? (
                    <div className="text-center py-8">
                      <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">Message Sent!</h3>
                      <p className="text-gray-600">We'll get back to you within 24 hours.</p>
                    </div>
                  ) : (
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Name *</label>
                          <Input
                            placeholder="Your full name"
                            value={formData.name}
                            onChange={(e) => handleInputChange("name", e.target.value)}
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Email *</label>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            value={formData.email}
                            onChange={(e) => handleInputChange("email", e.target.value)}
                            required
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Company</label>
                        <Input
                          placeholder="Your company name"
                          value={formData.company}
                          onChange={(e) => handleInputChange("company", e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Subject *</label>
                        <Input
                          placeholder="What's this about?"
                          value={formData.subject}
                          onChange={(e) => handleInputChange("subject", e.target.value)}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Message *</label>
                        <Textarea
                          placeholder="Tell us more about your inquiry..."
                          rows={5}
                          value={formData.message}
                          onChange={(e) => handleInputChange("message", e.target.value)}
                          required
                        />
                      </div>

                      <AnimatedButton
                        variant="bounce"
                        className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-3 rounded-lg font-medium"
                      >
                        <Send className="h-4 w-4 mr-2" />
                        Send Message
                      </AnimatedButton>
                    </form>
                  )}
                </CardContent>
              </Card>
            </AnimatedSection>

            {/* Contact Info */}
            <AnimatedSection animation="slideRight">
              <div className="space-y-8">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Offices</h3>
                  <div className="space-y-6">
                    {offices.map((office, index) => (
                      <AnimatedCard key={index} className="p-6 bg-gradient-to-br from-purple-50 to-blue-50 rounded-xl">
                        <h4 className="text-lg font-semibold text-gray-900 mb-2">{office.city}</h4>
                        <div className="space-y-2 text-gray-600">
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-2" />
                            <span>{office.address}</span>
                          </div>
                          <div className="flex items-center">
                            <Phone className="h-4 w-4 mr-2" />
                            <span>{office.phone}</span>
                          </div>
                          <div className="flex items-center">
                            <Mail className="h-4 w-4 mr-2" />
                            <span>{office.email}</span>
                          </div>
                        </div>
                      </AnimatedCard>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Support Hours</h3>
                  <AnimatedCard className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Monday - Friday</span>
                        <span className="font-semibold">9:00 AM - 6:00 PM PST</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Saturday</span>
                        <span className="font-semibold">10:00 AM - 4:00 PM PST</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Sunday</span>
                        <span className="font-semibold">Closed</span>
                      </div>
                      <div className="pt-3 border-t border-gray-200">
                        <div className="flex items-center">
                          <Headphones className="h-4 w-4 mr-2 text-purple-600" />
                          <span className="text-sm text-gray-600">24/7 AI Support Available</span>
                        </div>
                      </div>
                    </div>
                  </AnimatedCard>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <AnimatedSection animation="fadeIn" className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-600">Quick answers to common questions</p>
          </AnimatedSection>

          <StaggeredContainer className="space-y-6">
            {[
              {
                question: "How quickly can I get started?",
                answer:
                  "You can sign up and start using SkillMatch.ai immediately. Our onboarding process takes less than 10 minutes.",
              },
              {
                question: "Do you offer custom integrations?",
                answer:
                  "Yes! We offer custom integrations with your existing HR systems, ATS, and other tools. Contact our team to discuss your needs.",
              },
              {
                question: "What kind of support do you provide?",
                answer:
                  "We provide 24/7 AI-powered support, plus human support during business hours. Enterprise customers get dedicated account managers.",
              },
              {
                question: "Is there a free trial available?",
                answer:
                  "Yes, we offer a 14-day free trial with full access to all features. No credit card required to get started.",
              },
            ].map((faq, index) => (
              <AnimatedCard key={index}>
                <Card className="border-0 shadow-sm">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
                    <p className="text-gray-600">{faq.answer}</p>
                  </CardContent>
                </Card>
              </AnimatedCard>
            ))}
          </StaggeredContainer>
        </div>
      </section>
    </div>
  );
};

export default Contact;
