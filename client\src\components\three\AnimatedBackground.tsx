import React, { useRef, useMemo } from "react";
import { Canvas, use<PERSON>rame } from "@react-three/fiber";
import * as THREE from "three";

const AnimatedStars: React.FC = () => {
  const ref = useRef<THREE.Points>(null);

  const [positions, colors] = useMemo(() => {
    const positions = new Float32Array(1000 * 3);
    const colors = new Float32Array(1000 * 3);

    for (let i = 0; i < 1000; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 50;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 50;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 50;

      // Purple to blue gradient colors
      colors[i * 3] = 0.5 + Math.random() * 0.5; // R
      colors[i * 3 + 1] = 0.3 + Math.random() * 0.4; // G
      colors[i * 3 + 2] = 0.8 + Math.random() * 0.2; // B
    }

    return [positions, colors];
  }, []);

  useFrame((state) => {
    if (ref.current) {
      ref.current.rotation.x = state.clock.elapsedTime * 0.05;
      ref.current.rotation.y = state.clock.elapsedTime * 0.03;
    }
  });

  return (
    <points ref={ref}>
      <bufferGeometry>
        <bufferAttribute attach="attributes-position" count={positions.length / 3} array={positions} itemSize={3} />
        <bufferAttribute attach="attributes-color" count={colors.length / 3} array={colors} itemSize={3} />
      </bufferGeometry>
      <pointsMaterial transparent color="#8b5cf6" size={0.5} sizeAttenuation={true} depthWrite={false} vertexColors />
    </points>
  );
};

const WaveGeometry: React.FC = () => {
  const meshRef = useRef<THREE.Mesh>(null);
  const geometryRef = useRef<THREE.PlaneGeometry>(null);

  useFrame((state) => {
    if (meshRef.current && geometryRef.current) {
      const positions = geometryRef.current.attributes.position;
      const time = state.clock.elapsedTime;

      for (let i = 0; i < positions.count; i++) {
        const x = positions.getX(i);
        const y = positions.getY(i);
        const z = Math.sin(x * 0.5 + time) * Math.cos(y * 0.5 + time) * 0.5;
        positions.setZ(i, z);
      }

      positions.needsUpdate = true;
      meshRef.current.rotation.z = time * 0.1;
    }
  });

  return (
    <mesh ref={meshRef} position={[0, 0, -10]} rotation={[-Math.PI / 4, 0, 0]}>
      <planeGeometry ref={geometryRef} args={[20, 20, 32, 32]} />
      <meshStandardMaterial color="#3b82f6" transparent opacity={0.1} wireframe />
    </mesh>
  );
};

const FloatingOrbs: React.FC = () => {
  const orbsRef = useRef<THREE.Group>(null);

  const orbs = useMemo(() => {
    return Array.from({ length: 8 }, (_, i) => ({
      position: [
        Math.cos((i * Math.PI * 2) / 8) * 5,
        Math.sin((i * Math.PI * 2) / 8) * 3,
        Math.sin((i * Math.PI * 4) / 8) * 2,
      ] as [number, number, number],
      color: i % 2 === 0 ? "#8b5cf6" : "#3b82f6",
      scale: 0.3 + Math.random() * 0.4,
    }));
  }, []);

  useFrame((state) => {
    if (orbsRef.current) {
      orbsRef.current.rotation.y = state.clock.elapsedTime * 0.2;
      orbsRef.current.children.forEach((child, i) => {
        child.position.y += Math.sin(state.clock.elapsedTime * 2 + i) * 0.01;
      });
    }
  });

  return (
    <group ref={orbsRef}>
      {orbs.map((orb, index) => (
        <mesh key={index} position={orb.position} scale={orb.scale}>
          <sphereGeometry args={[1, 16, 16]} />
          <meshStandardMaterial
            color={orb.color}
            transparent
            opacity={0.6}
            emissive={orb.color}
            emissiveIntensity={0.2}
          />
        </mesh>
      ))}
    </group>
  );
};

const AnimatedBackgroundScene: React.FC = () => {
  return (
    <>
      <ambientLight intensity={0.3} />
      <pointLight position={[10, 10, 10]} intensity={0.5} color="#8b5cf6" />
      <pointLight position={[-10, -10, -10]} intensity={0.3} color="#3b82f6" />

      <AnimatedStars />
      <WaveGeometry />
      <FloatingOrbs />
    </>
  );
};

interface AnimatedBackgroundProps {
  className?: string;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({ className = "" }) => {
  return (
    <div className={`fixed inset-0 -z-10 ${className}`}>
      <Canvas camera={{ position: [0, 0, 5], fov: 75 }}>
        <AnimatedBackgroundScene />
      </Canvas>
    </div>
  );
};

export default AnimatedBackground;
