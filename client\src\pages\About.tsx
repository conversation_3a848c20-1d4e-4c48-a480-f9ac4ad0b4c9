import AnimatedSection, {
  AnimatedCard,
  FloatingElement,
  StaggeredContainer,
} from "@/components/animations/AnimatedSection";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Award, Globe, Heart, Lightbulb, Target, Zap } from "lucide-react";
import { Link } from "react-router-dom";
import Navigation from "@/components/Navigation";

const About = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/80 backdrop-blur-md border-b border-gray-200 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">SM</span>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                SkillMatch.ai
              </span>
            </Link>
            <div className="flex items-center space-x-4">
              <Link
                to="/features"
                className="px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium"
              >
                Features
              </Link>
              <Link
                to="/pricing"
                className="px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium"
              >
                Pricing
              </Link>
              <Link
                to="/contact"
                className="px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium"
              >
                Contact
              </Link>
              <Link to="/login">
                <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <AnimatedSection animation="slideLeft">
                <Badge className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 text-sm font-medium mb-6">
                  About SkillMatch.ai
                </Badge>
                <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
                  Revolutionizing
                  <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent block">
                    Talent Matching
                  </span>
                </h1>
                <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                  We're on a mission to bridge the gap between talented students and innovative companies through the
                  power of artificial intelligence and machine learning.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link to="/contact">
                    <Button
                      size="lg"
                      className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    >
                      Get in Touch
                    </Button>
                  </Link>
                  <Link to="/careers">
                    <Button size="lg" variant="outline" className="border-purple-200 hover:bg-purple-50">
                      Join Our Team
                    </Button>
                  </Link>
                </div>
              </AnimatedSection>
            </div>

            <AnimatedSection animation="slideRight" className="h-96">
              <FloatingElement intensity={5} speed={3}>
                <div className="h-full bg-gradient-to-br from-purple-100 to-blue-100 rounded-2xl flex items-center justify-center relative overflow-hidden">
                  <div className="absolute inset-0">
                    <div className="w-32 h-32 bg-purple-300 rounded-full absolute top-1/4 left-1/4 animate-float opacity-60" />
                    <div className="w-24 h-24 bg-blue-300 rounded-full absolute bottom-1/4 right-1/4 animate-float-delayed opacity-60" />
                    <div className="w-40 h-40 bg-indigo-300 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 animate-float-slow opacity-40" />
                  </div>
                  <div className="relative z-10 text-center">
                    <div className="text-6xl mb-4">🚀</div>
                    <h3 className="text-2xl font-bold text-gray-800">Innovation</h3>
                    <p className="text-gray-600">Powered by AI</p>
                  </div>
                </div>
              </FloatingElement>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <AnimatedSection animation="fadeIn" className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Mission & Vision</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Empowering the future workforce through intelligent matching and skill development
            </p>
          </AnimatedSection>

          <StaggeredContainer className="grid md:grid-cols-2 gap-8">
            <AnimatedCard className="p-8 bg-gradient-to-br from-purple-50 to-blue-50 rounded-2xl">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center mr-4">
                  <Target className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">Our Mission</h3>
              </div>
              <p className="text-gray-600 text-lg leading-relaxed">
                To democratize access to career opportunities by leveraging AI to match students with the perfect
                internships and jobs based on their skills, interests, and potential.
              </p>
            </AnimatedCard>

            <AnimatedCard className="p-8 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-4">
                  <Lightbulb className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900">Our Vision</h3>
              </div>
              <p className="text-gray-600 text-lg leading-relaxed">
                A world where every student finds their ideal career path and every company discovers exceptional
                talent, creating a more efficient and equitable job market.
              </p>
            </AnimatedCard>
          </StaggeredContainer>
        </div>
      </section>

      {/* Values */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <AnimatedSection animation="fadeIn" className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Core Values</h2>
            <p className="text-xl text-gray-600">The principles that guide everything we do</p>
          </AnimatedSection>

          <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <AnimatedCard className="text-center p-6" hoverScale={1.05}>
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Empathy</h3>
              <p className="text-gray-600">
                Understanding the challenges faced by both students and employers in today's job market.
              </p>
            </AnimatedCard>

            <AnimatedCard className="text-center p-6" hoverScale={1.05}>
              <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Innovation</h3>
              <p className="text-gray-600">
                Continuously pushing the boundaries of what's possible with AI and technology.
              </p>
            </AnimatedCard>

            <AnimatedCard className="text-center p-6" hoverScale={1.05}>
              <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Globe className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Inclusivity</h3>
              <p className="text-gray-600">
                Creating equal opportunities for students from all backgrounds and experiences.
              </p>
            </AnimatedCard>

            <AnimatedCard className="text-center p-6" hoverScale={1.05}>
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Award className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Excellence</h3>
              <p className="text-gray-600">Delivering the highest quality platform and support to our users.</p>
            </AnimatedCard>
          </StaggeredContainer>
        </div>
      </section>

      {/* Stats */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-blue-600">
        <div className="max-w-7xl mx-auto">
          <AnimatedSection animation="fadeIn" className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Our Impact</h2>
            <p className="text-purple-100 text-lg">
              Making a difference in the lives of students and companies worldwide
            </p>
          </AnimatedSection>

          <StaggeredContainer className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="text-white">
              <div className="text-4xl font-bold mb-2">10K+</div>
              <div className="text-purple-100">Students Matched</div>
            </div>
            <div className="text-white">
              <div className="text-4xl font-bold mb-2">500+</div>
              <div className="text-purple-100">Partner Companies</div>
            </div>
            <div className="text-white">
              <div className="text-4xl font-bold mb-2">95%</div>
              <div className="text-purple-100">Success Rate</div>
            </div>
            <div className="text-white">
              <div className="text-4xl font-bold mb-2">50+</div>
              <div className="text-purple-100">Countries</div>
            </div>
          </StaggeredContainer>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-4xl mx-auto text-center">
          <AnimatedSection animation="scale">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Ready to Transform Your Career Journey?</h2>
            <p className="text-xl text-gray-600 mb-8">
              Join thousands of students and companies who have found success with SkillMatch.ai
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/signup">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                >
                  Get Started Today
                </Button>
              </Link>
              <Link to="/contact">
                <Button size="lg" variant="outline" className="border-purple-200 hover:bg-purple-50">
                  Contact Sales
                </Button>
              </Link>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </div>
  );
};

export default About;
