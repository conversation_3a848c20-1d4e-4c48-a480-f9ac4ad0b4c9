import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Brain,
  Zap,
  Target,
  Users,
  BarChart3,
  Shield,
  Clock,
  Sparkles,
  Cpu,
  Database,
  Network,
  Rocket,
} from "lucide-react";
import { Link } from "react-router-dom";
import AnimatedSection from "@/components/animations/AnimatedSection";
import { AnimatedCard, StaggeredContainer, FloatingElement } from "@/components/animations/AnimatedSection";

const Features = () => {
  const features = [
    {
      icon: Brain,
      title: "AI-Powered Matching",
      description:
        "Advanced machine learning algorithms analyze skills, experience, and preferences to find perfect matches.",
      color: "from-purple-600 to-blue-600",
      details: ["Natural Language Processing", "Skill Gap Analysis", "Personality Matching", "Cultural Fit Assessment"],
    },
    {
      icon: Zap,
      title: "Real-time Recommendations",
      description:
        "Get instant job recommendations as soon as new opportunities that match your profile become available.",
      color: "from-blue-600 to-indigo-600",
      details: ["Live Job Alerts", "Smart Notifications", "Priority Matching", "Instant Updates"],
    },
    {
      icon: Target,
      title: "Precision Targeting",
      description:
        "Our AI ensures that students are matched with roles that align with their career goals and skill sets.",
      color: "from-indigo-600 to-purple-600",
      details: ["Career Path Analysis", "Goal Alignment", "Skill Progression", "Industry Insights"],
    },
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      description:
        "Comprehensive dashboards and reports provide insights into hiring trends and candidate performance.",
      color: "from-purple-600 to-pink-600",
      details: ["Performance Metrics", "Hiring Trends", "Success Rates", "ROI Analysis"],
    },
    {
      icon: Shield,
      title: "Secure & Private",
      description: "Enterprise-grade security ensures your data is protected with end-to-end encryption.",
      color: "from-pink-600 to-red-600",
      details: ["End-to-End Encryption", "GDPR Compliant", "SOC 2 Certified", "Privacy Controls"],
    },
    {
      icon: Clock,
      title: "24/7 Support",
      description: "Round-the-clock AI-powered support and human assistance when you need it most.",
      color: "from-red-600 to-orange-600",
      details: ["AI Chatbot", "Human Support", "Knowledge Base", "Video Tutorials"],
    },
  ];

  const techFeatures = [
    {
      icon: Cpu,
      title: "Machine Learning Engine",
      description: "Continuously learning and improving match accuracy",
      stats: "99.2% accuracy",
    },
    {
      icon: Database,
      title: "Big Data Processing",
      description: "Processing millions of data points in real-time",
      stats: "10M+ profiles",
    },
    {
      icon: Network,
      title: "Neural Networks",
      description: "Deep learning for complex pattern recognition",
      stats: "50+ parameters",
    },
    {
      icon: Rocket,
      title: "Cloud Infrastructure",
      description: "Scalable and reliable cloud-based platform",
      stats: "99.9% uptime",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/80 backdrop-blur-md border-b border-gray-200 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">SM</span>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                SkillMatch.ai
              </span>
            </Link>
            <div className="flex items-center space-x-4">
              <Link
                to="/about"
                className="px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium"
              >
                About
              </Link>
              <Link
                to="/pricing"
                className="px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium"
              >
                Pricing
              </Link>
              <Link
                to="/contact"
                className="px-3 py-2 text-gray-700 hover:text-purple-600 transition-colors duration-200 font-medium"
              >
                Contact
              </Link>
              <Link to="/login">
                <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <AnimatedSection animation="fadeIn">
            <Badge className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 text-sm font-medium mb-6">
              🚀 Cutting-Edge Features
            </Badge>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Powerful Features for
              <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent block">
                Smart Matching
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Discover the advanced AI-powered features that make SkillMatch.ai the most effective platform for
              connecting talent with opportunities.
            </p>
          </AnimatedSection>
        </div>
      </section>

      {/* Main Features */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <AnimatedSection animation="fadeIn" className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Core Features</h2>
            <p className="text-xl text-gray-600">Everything you need for successful talent matching</p>
          </AnimatedSection>

          <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <AnimatedCard key={index} className="h-full" hoverScale={1.02}>
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 h-full">
                  <CardHeader>
                    <div
                      className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-lg flex items-center justify-center mb-4`}
                    >
                      <feature.icon className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                    <CardDescription className="text-gray-600">{feature.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {feature.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex items-center text-sm text-gray-600">
                          <Sparkles className="h-4 w-4 text-purple-500 mr-2" />
                          {detail}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </AnimatedCard>
            ))}
          </StaggeredContainer>
        </div>
      </section>

      {/* Technical Features */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <AnimatedSection animation="fadeIn" className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Technical Excellence</h2>
            <p className="text-xl text-gray-600">Built on cutting-edge technology for maximum performance</p>
          </AnimatedSection>

          <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {techFeatures.map((tech, index) => (
              <AnimatedCard key={index} hoverScale={1.05}>
                <div className="text-center p-6 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl">
                  <FloatingElement intensity={3} speed={2 + index * 0.5}>
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <tech.icon className="h-8 w-8 text-white" />
                    </div>
                  </FloatingElement>
                  <h3 className="text-lg font-semibold mb-2">{tech.title}</h3>
                  <p className="text-gray-600 text-sm mb-3">{tech.description}</p>
                  <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                    {tech.stats}
                  </Badge>
                </div>
              </AnimatedCard>
            ))}
          </StaggeredContainer>
        </div>
      </section>

      {/* Feature Comparison */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-blue-600">
        <div className="max-w-7xl mx-auto">
          <AnimatedSection animation="fadeIn" className="text-center mb-16">
            <h2 className="text-3xl font-bold text-white mb-4">Why Choose SkillMatch.ai?</h2>
            <p className="text-purple-100 text-lg">See how we compare to traditional hiring methods</p>
          </AnimatedSection>

          <StaggeredContainer className="grid md:grid-cols-3 gap-8 text-center">
            <div className="text-white">
              <div className="text-4xl font-bold mb-2">10x</div>
              <div className="text-purple-100">Faster Matching</div>
            </div>
            <div className="text-white">
              <div className="text-4xl font-bold mb-2">95%</div>
              <div className="text-purple-100">Accuracy Rate</div>
            </div>
            <div className="text-white">
              <div className="text-4xl font-bold mb-2">80%</div>
              <div className="text-purple-100">Cost Reduction</div>
            </div>
          </StaggeredContainer>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-4xl mx-auto text-center">
          <AnimatedSection animation="scale">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Experience the Future of Hiring</h2>
            <p className="text-xl text-gray-600 mb-8">
              Join thousands of companies and students who have revolutionized their hiring process
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/signup">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                >
                  Start Free Trial
                </Button>
              </Link>
              <Link to="/demo">
                <Button size="lg" variant="outline" className="border-purple-200 hover:bg-purple-50">
                  Request Demo
                </Button>
              </Link>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </div>
  );
};

export default Features;
