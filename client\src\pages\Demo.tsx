import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { <PERSON>, Pause, RotateCcw, Maximize, Settings, Eye, MousePointer, Zap } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import AnimatedSection from "@/components/animations/AnimatedSection";
import { AnimatedCard, AnimatedButton } from "@/components/animations/AnimatedSection";
import Navigation from "@/components/Navigation";
// import FloatingCubes from '@/components/three/FloatingCubes';

const Demo = () => {
  const [isPlaying, setIsPlaying] = useState(true);
  const [demoStep, setDemoStep] = useState(1);

  const demoSteps = [
    {
      title: "Upload Resume",
      description: "Students upload their resumes and our AI extracts key skills and experience",
      icon: "📄",
    },
    {
      title: "AI Analysis",
      description: "Advanced algorithms analyze skills, preferences, and career goals",
      icon: "🤖",
    },
    {
      title: "Smart Matching",
      description: "AI finds the perfect job matches based on comprehensive analysis",
      icon: "🎯",
    },
    {
      title: "Interview Process",
      description: "Automated screening and interview scheduling with employers",
      icon: "💼",
    },
    {
      title: "Success!",
      description: "Students get hired and start their dream careers",
      icon: "🎉",
    },
  ];

  const features = [
    {
      title: "Real-time Matching",
      description: "See matches appear instantly as new opportunities become available",
      demo: "Live demo of matching algorithm",
    },
    {
      title: "AI Interview Assistant",
      description: "Experience our AI-powered interview preparation and practice",
      demo: "Interactive interview simulation",
    },
    {
      title: "Skill Assessment",
      description: "Try our adaptive skill assessment that learns as you answer",
      demo: "Sample coding challenge",
    },
    {
      title: "Analytics Dashboard",
      description: "Explore comprehensive analytics and insights for employers",
      demo: "Interactive dashboard preview",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <AnimatedSection animation="slideLeft">
                <Badge className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 text-sm font-medium mb-6">
                  🎮 Interactive Demo
                </Badge>
                <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
                  Experience
                  <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent block">
                    SkillMatch.ai
                  </span>
                  <span className="text-4xl md:text-5xl block mt-2 text-gray-700">In Action</span>
                </h1>
                <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                  See how our AI-powered platform transforms the hiring process. Try our interactive demo and experience
                  the future of talent matching.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <AnimatedButton
                    variant="bounce"
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-4 rounded-lg text-lg"
                  >
                    <Play className="mr-2 h-5 w-5" />
                    Start Demo
                  </AnimatedButton>
                  <Link to="/signup">
                    <Button size="lg" variant="outline" className="border-purple-200 hover:bg-purple-50">
                      Skip to Sign Up
                    </Button>
                  </Link>
                </div>
              </AnimatedSection>
            </div>

            <AnimatedSection animation="slideRight" className="h-96">
              <div className="relative h-full">
                <div className="h-full rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-600 flex items-center justify-center relative">
                  {/* Animated background elements */}
                  <div className="absolute inset-0">
                    <div className="w-32 h-32 bg-white/20 rounded-full absolute top-1/4 left-1/4 animate-float" />
                    <div className="w-24 h-24 bg-white/15 rounded-full absolute bottom-1/4 right-1/4 animate-float-delayed" />
                    <div className="w-40 h-40 bg-white/10 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 animate-float-slow" />

                    {/* Geometric shapes */}
                    <div className="absolute top-1/3 right-1/3 w-16 h-16 border-2 border-white/30 rotate-45 animate-spin-slow" />
                    <div className="absolute bottom-1/3 left-1/3 w-12 h-12 border-2 border-white/20 animate-spin-reverse" />
                  </div>

                  {/* Center content */}
                  <div className="relative z-10 text-center text-white">
                    <div className="text-6xl mb-4">🎮</div>
                    <h3 className="text-2xl font-bold mb-2">Interactive Demo</h3>
                    <p className="text-purple-100">Experience AI in Action</p>
                  </div>
                </div>

                <div className="absolute top-4 right-4 flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="bg-white/80 backdrop-blur-sm"
                    onClick={() => setIsPlaying(!isPlaying)}
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <Button size="sm" variant="outline" className="bg-white/80 backdrop-blur-sm">
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline" className="bg-white/80 backdrop-blur-sm">
                    <Maximize className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Demo Process */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <AnimatedSection animation="fadeIn" className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">How It Works</h2>
            <p className="text-xl text-gray-600">Follow the journey from application to hire</p>
          </AnimatedSection>

          <div className="relative">
            {/* Progress Line */}
            <div className="absolute top-8 left-0 right-0 h-1 bg-gray-200 rounded-full">
              <div
                className="h-full bg-gradient-to-r from-purple-600 to-blue-600 rounded-full transition-all duration-1000"
                style={{ width: `${(demoStep / demoSteps.length) * 100}%` }}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-5 gap-8 relative">
              {demoSteps.map((step, index) => (
                <AnimatedCard key={index} className="text-center">
                  <div
                    className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-2xl transition-all duration-300 ${
                      index + 1 <= demoStep
                        ? "bg-gradient-to-r from-purple-600 to-blue-600 text-white"
                        : "bg-gray-200 text-gray-400"
                    }`}
                  >
                    {step.icon}
                  </div>
                  <h3 className="text-lg font-semibold mb-2">{step.title}</h3>
                  <p className="text-gray-600 text-sm">{step.description}</p>
                </AnimatedCard>
              ))}
            </div>

            <div className="flex justify-center mt-8 space-x-4">
              <Button
                variant="outline"
                onClick={() => setDemoStep(Math.max(1, demoStep - 1))}
                disabled={demoStep === 1}
              >
                Previous
              </Button>
              <Button
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                onClick={() => setDemoStep(Math.min(demoSteps.length, demoStep + 1))}
                disabled={demoStep === demoSteps.length}
              >
                Next Step
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Features */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <AnimatedSection animation="fadeIn" className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Try Our Features</h2>
            <p className="text-xl text-gray-600">Interactive demos of our core capabilities</p>
          </AnimatedSection>

          <div className="grid md:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <AnimatedCard key={index} hoverScale={1.02}>
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 h-full">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-4">
                      <CardTitle className="text-xl">{feature.title}</CardTitle>
                      <div className="flex space-x-2">
                        <Eye className="h-5 w-5 text-purple-600" />
                        <MousePointer className="h-5 w-5 text-blue-600" />
                      </div>
                    </div>
                    <CardDescription className="text-gray-600">{feature.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg p-6 mb-4">
                      <div className="flex items-center justify-center h-32 text-gray-500">
                        <div className="text-center">
                          <Zap className="h-8 w-8 mx-auto mb-2" />
                          <p className="text-sm">{feature.demo}</p>
                        </div>
                      </div>
                    </div>
                    <AnimatedButton
                      variant="bounce"
                      className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-2 rounded-lg"
                    >
                      Try Demo
                    </AnimatedButton>
                  </CardContent>
                </Card>
              </AnimatedCard>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-blue-600">
        <div className="max-w-4xl mx-auto text-center">
          <AnimatedSection animation="scale">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
            <p className="text-xl text-purple-100 mb-8">
              Experience the full power of SkillMatch.ai with your own account
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/signup">
                <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
                  Create Free Account
                </Button>
              </Link>
              <Link to="/contact">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-purple-600"
                >
                  Schedule Demo Call
                </Button>
              </Link>
            </div>
          </AnimatedSection>
        </div>
      </section>
    </div>
  );
};

export default Demo;
